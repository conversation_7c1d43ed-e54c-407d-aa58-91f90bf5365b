<?php
namespace nl\actinum\custom\tables {

//[GENERATEDBYBUILDER
/**
* @Entity
* @ChangeTrackingPolicy("DEFERRED_EXPLICIT")
* @HasLifecycleCallbacks
*/

//GENERATEDBYBUILDER]

class file extends \nl\actinum\generated\tables\file{

    //we kunnen een file object maken, die data bevat. Bij het flushen van de entity (dus als het record id bekend is,
    //wordt het bestand weggeschreven naar disk.
    //zodra dit is gedaan is ook het path bekend. Daarvoor wordt er een exception gegooid.
    //de getdata retourneert de tijdelijke data, of, indien het path bestaat, de data van disk.

    protected $path = null;
    protected $data = null;
    const METOPMAAK = 1;
    const ZONDEROPMAAK = 2;


    //hoeft niet geversioned te worden.
    /** @Column(type="integer", nullable=true) */
    protected $version;

    /** @prePersist */
    public function checkElementid(){
        if (!$this->elementid){
            throw new \Exception('Elementid should be set in file.');
        }
    }


    /** @postPersist */
    public function writeFileToDisk(){
        $path = $this->getPath(); //id known, dus path klopt
        if (!is_file($path)){
            $pathfrags = explode("/", $path);
            unset($pathfrags[count($pathfrags)-1]);
            $dir = implode("/", $pathfrags);

            if (!is_dir($dir)){
                mkdir($dir, 0777, true);
            }
            // chmod($dir, 0777);

            $data = $this->getData();
            file_put_contents($path, $data);
            $this->path = $path;
        }
        return $path;
    }

    //flush the contents to a temp file, returns the fully specified file name
    public function writeToTemp($ext = null){
        $tempname = tempnam(\nl\actinum\framework\application\util\File::getUserfilesTempdir(), 'ww');

        if ($ext !== null) {
            $tempname .= '.' . $ext;
        }

        $data = $this->getData();
        file_put_contents($tempname, $data);

        return $tempname;
    }

    public function getData(){
        if (is_null($this->data)){
            $path = $this->getPath();
            if (is_file($path)){
                return file_get_contents($path);
            }
            else{
                return 'FILE NOT FOUND';
            }
        }
        else{
            return $this->data;
        }
    }

    public function setData($data){
        $this->data = $data;
    }


    public function getPath(){
        if ($this->id){
            $basedir = static::getBasedir();

            if ($this->id > PHP_INT_MAX){
                throw new \Exception('Max INT reached. Could not determine filename accurately.');
            }

            $filename = $basedir.'/'.\nl\actinum\framework\application\util\File::convertIdToSubdirs($this->id).'/'.$this->getTitle();
            return $filename;
        }
        else{
            throw new \Exception('File entity not yet flushed, so no ID and Path known');
        }
    }

    public static function getBasedir(){
        $basedirconfig = \nl\actinum\framework\application\Registry::get('config')->filemanagers->localfilesystem->basedir;
        $tenant = \nl\actinum\framework\application\Registry::get('databasesettings')->getTenant();
        $tenantid = $tenant['id'];
        $basedir = $basedirconfig.'/tenant_'.$tenantid;
        if (!is_dir($basedir)){
            mkdir($basedir, 0777, true);
            chmod($basedir, 0777);
        }
        return $basedir;
    }

    public function updateZoeknaam(){
        $this->zoeknaam = $this->title;
    }

    public function flushData($method = 'flush', array $extraheaders = []){
        $headers = [
            'Expires' => 'Mon, 26 Jul 1997 05:00:00 GMT',
            'Last-Modified' => gmdate("D, d M Y H:i:s") . " GMT"
        ];
        $headers = array_merge($headers, $extraheaders);
        //$file = $this->getSplFileObject($filerecord);

        foreach($headers as $key => $value){
            header($key.': '.$value);
        }


        //print_r($file);
        $finfo = new \finfo;
        $data = $this->getData();
        $title = $this->getTitle();


        $mimeType     = $finfo->buffer($data, FILEINFO_MIME_TYPE);
        $mimeEncoding = $finfo->buffer($data, FILEINFO_MIME_ENCODING);
        $ext          = strtolower(pathinfo($title, PATHINFO_EXTENSION));

        if($mimeType == 'application/zip' || $mimeType == 'application/x-zip') {
            $mapping = [
                'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'dotx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.template',
                'docm' => 'application/vnd.ms-word.document.macroEnabled.12',
                'dotm' => 'application/vnd.ms-word.template.macroEnabled.12',
                'xlsx' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'xltx' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.template',
                'xlsm' => 'application/vnd.ms-excel.sheet.macroEnabled.12',
                'xltm' => 'application/vnd.ms-excel.template.macroEnabled.12',
                'xlam' => 'application/vnd.ms-excel.addin.macroEnabled.12',
                'xlsb' => 'application/vnd.ms-excel.sheet.binary.macroEnabled.12',
                'pptx' => 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
                'potx' => 'application/vnd.openxmlformats-officedocument.presentationml.template',
                'ppsx' => 'application/vnd.openxmlformats-officedocument.presentationml.slideshow',
                'ppam' => 'application/vnd.ms-powerpoint.addin.macroEnabled.12',
                'pptm' => 'application/vnd.ms-powerpoint.presentation.macroEnabled.12',
                'potm' => 'application/vnd.ms-powerpoint.template.macroEnabled.12',
                'ppsm' => 'application/vnd.ms-powerpoint.slideshow.macroEnabled.12',
            ];

            if(array_key_exists($ext, $mapping)) {
                $mimeType = $mapping[$ext];
            }
        }


        header("Content-type: {$mimeType}; charset={$mimeEncoding}");
        if ($method == 'download'){
            header("Content-Disposition: attachment; filename=\"" . $title . "\"");
        }
        else{
            header("Content-Disposition: inline; filename=\"" . $title . "\"");
        }

        //header("Content-Length: " . $file->getSize());
        header("Content-Transfer-Encoding: binary");

        print $data;

        exit;
    }

}
}
?>
