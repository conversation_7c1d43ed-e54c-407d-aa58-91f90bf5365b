<?php
namespace nl\actinum\custom\elements\specific {



/*[GENERATEDBYBUILDER_CLASSNAME*/
class n12998n extends \nl\actinum\custom\elements\generic\Forminputstring
/*GENERATEDBYBUILDER_CLASSNAME]*/
    //implements \nl\actinum\framework\application\interfaces\IHasCustomClientSideCode
                                                                                                                            {

/*[GENERATEDBYBUILDER*/

    public static $subtitle = 'Label waarde 3';
    public static $showsubtitle = true;
    public static $dbfield = 'chemischlabel3';
    public static $defaultvalue = NULL;
    public static $nullallowed = NULL;
    public static $dbtrigger = NULL;
    public static $index = NULL;
    public static $placeholder = NULL;
    public static $required = NULL;
    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n12998n';
    public static $parentElementid = 'n12961n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    public static $childrenElementIds =  [
];
/*GENERATEDBYBUILDER]*/

//    public static function getJs(){
//        $result = parent::getJs();
//        $result['Actinum.Application.CustomJs.n12998n'] =
//<<<'JS'
//Actinum.Application.CustomElements.n12998n = new Class({ Extends: Actinum.Application.Elements.Forminputstring,
//});
//JS;
//        return $result;
//    }
//
//
//
//    public static function getCss(){
//        $result = parent::getCss();
//        $result['Actinum.Application.CustomCss.n12998n'] =
//<<<'CSS'
//CSS;
//        return $result;
//    }


        


}

}
?>