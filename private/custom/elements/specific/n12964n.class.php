<?php
namespace nl\actinum\custom\elements\specific {



/*[GENERATEDBYBUILDER_CLASSNAME*/
class n12964n extends \nl\actinum\custom\elements\generic\Forminputcontainer
/*GENERATEDBYBUILDER_CLASSNAME]*/
    //implements \nl\actinum\framework\application\interfaces\IHasCustomClientSideCode
                                                                                                                            {

/*[GENERATEDBYBUILDER*/

    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n12964n';
    public static $parentElementid = 'n12957n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    public static $childrenElementIds =  [
  0 => 'n12965n',
  1 => 'n12966n',
  2 => 'n12999n',
  3 => 'n13000n',
];
/*GENERATEDBYBUILDER]*/

//    public static function getJs(){
//        $result = parent::getJs();
//        $result['Actinum.Application.CustomJs.n12964n'] =
//<<<'JS'
//Actinum.Application.CustomElements.n12964n = new Class({ Extends: Actinum.Application.Elements.Forminputcontainer,
//});
//JS;
//        return $result;
//    }
//
//
//
//    public static function getCss(){
//        $result = parent::getCss();
//        $result['Actinum.Application.CustomCss.n12964n'] =
//<<<'CSS'
//CSS;
//        return $result;
//    }


        


}

}
?>