<?php
namespace nl\actinum\custom\elements\specific {



/*[GENERATEDBYBUILDER_CLASSNAME*/
class n13049n extends \nl\actinum\custom\elements\generic\Forminputcontainerlabel
/*GENERATEDBYBUILDER_CLASSNAME]*/
    //implements \nl\actinum\framework\application\interfaces\IHasCustomClientSideCode
                                                                                                                            {

/*[GENERATEDBYBUILDER*/

    public static $title = 'allergen';
    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n13049n';
    public static $parentElementid = 'n13048n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    public static $childrenElementIds = [
];
/*GENERATEDBYBUILDER]*/

//    public static function getJs(){
//        $result = parent::getJs();
//        $result['Actinum.Application.CustomJs.n13049n'] =
//<<<'JS'
//Actinum.Application.CustomElements.n13049n = new Class({ Extends: Actinum.Application.Elements.Forminputcontainerlabel,
//});
//JS;
//        return $result;
//    }
//
//
//
//    public static function getCss(){
//        $result = parent::getCss();
//        $result['Actinum.Application.CustomCss.n13049n'] =
//<<<'CSS'
//CSS;
//        return $result;
//    }


        


}

}
?>