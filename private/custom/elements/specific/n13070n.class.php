<?php
namespace nl\actinum\custom\elements\specific {



/*[GENERATEDBYBUILDER_CLASSNAME*/
class n13070n extends \nl\actinum\custom\elements\generic\Blockpopup
/*GENERATEDBYBUILDER_CLASSNAME]*/
    //implements \nl\actinum\framework\application\interfaces\IHasCustomClientSideCode
                                                                                                                            {

/*[GENERATEDBYBUILDER*/

    public static $title = 'Interne productspecificatie downloaden....';
    public static $style = '._this_ {
  width: 500px;
}
';
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n13070n';
    public static $parentElementid = 'n11926n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    public static $childrenElementIds = [
  0 => 'n13071n',
  1 => 'n13076n',
  2 => 'n13075n',
];
/*GENERATEDBYB<PERSON>LDER]*/

//    public static function getJs(){
//        $result = parent::getJs();
//        $result['Actinum.Application.CustomJs.n13070n'] =
//<<<'JS'
//Actinum.Application.CustomElements.n13070n = new Class({ Extends: Actinum.Application.Elements.Blockpopup,
//});
//JS;
//        return $result;
//    }
//
//
//
//    public static function getCss(){
//        $result = parent::getCss();
//        $result['Actinum.Application.CustomCss.n13070n'] =
//<<<'CSS'
//CSS;
//        return $result;
//    }


        


}

}
?>