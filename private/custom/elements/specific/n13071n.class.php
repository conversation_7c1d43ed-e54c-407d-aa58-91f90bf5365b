<?php
namespace nl\actinum\custom\elements\specific {



/*[GENERATEDBYBUILDER_CLASSNAME*/
class n13071n extends \nl\actinum\custom\elements\generic\Html
/*GENERATEDBYBUILDER_CLASSNAME]*/
    //implements \nl\actinum\framework\application\interfaces\IHasCustomClientSideCode
                                                                                                                            {

/*[GENERATEDBYBUILDER*/

    public static $html = NULL;
    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n13071n';
    public static $parentElementid = 'n13070n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    public static $childrenElementIds = [
];
/*GENERATEDBYBUILDER]*/

//    public static function getJs(){
//        $result = parent::getJs();
//        $result['Actinum.Application.CustomJs.n13071n'] =
//<<<'JS'
//Actinum.Application.CustomElements.n13071n = new Class({ Extends: Actinum.Application.Elements.Html,
//});
//JS;
//        return $result;
//    }
//
//
//
//    public static function getCss(){
//        $result = parent::getCss();
//        $result['Actinum.Application.CustomCss.n13071n'] =
//<<<'CSS'
//CSS;
//        return $result;
//    }


        


}

}
?>