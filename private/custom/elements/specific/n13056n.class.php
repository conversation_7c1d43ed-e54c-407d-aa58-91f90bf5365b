<?php
namespace nl\actinum\custom\elements\specific {



/*[GENERATEDBYBUILDER_CLASSNAME*/
class n13056n extends \nl\actinum\custom\elements\generic\Forminputfloat
/*GENERATEDBYBUILDER_CLASSNAME]*/
    //implements \nl\actinum\framework\application\interfaces\IHasCustomClientSideCode
                                                                                                                            {

/*[GENERATEDBYBUILDER*/

    public static $subtitle = 'ppm';
    public static $showsubtitle = NULL;
    public static $dbfield = 'ppm';
    public static $defaultvalue = NULL;
    public static $nullallowed = NULL;
    public static $dbtrigger = NULL;
    public static $index = NULL;
    public static $placeholder = NULL;
    public static $required = NULL;
    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n13056n';
    public static $parentElementid = 'n13054n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    public static $childrenElementIds = [
];
/*GENERATEDBYBUILDER]*/

//    public static function getJs(){
//        $result = parent::getJs();
//        $result['Actinum.Application.CustomJs.n13056n'] =
//<<<'JS'
//Actinum.Application.CustomElements.n13056n = new Class({ Extends: Actinum.Application.Elements.Forminputfloat,
//});
//JS;
//        return $result;
//    }
//
//
//
//    public static function getCss(){
//        $result = parent::getCss();
//        $result['Actinum.Application.CustomCss.n13056n'] =
//<<<'CSS'
//CSS;
//        return $result;
//    }


        


}

}
?>