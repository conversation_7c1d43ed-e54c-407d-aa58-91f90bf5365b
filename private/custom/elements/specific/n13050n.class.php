<?php
namespace nl\actinum\custom\elements\specific {



/*[GENERATEDBYBUILDER_CLASSNAME*/
class n13050n extends \nl\actinum\custom\elements\generic\Forminputdropdown
/*GENERATEDBYBUILDER_CLASSNAME]*/
    //implements \nl\actinum\framework\application\interfaces\IHasCustomClientSideCode
                                                                                                                            {

/*[GENERATEDBYBUILDER*/

    public static $tablename = 'allergen';
    public static $selectdql = 'allergen.name';
    public static $aliaspostfix = NULL;
    public static $detailpage = NULL;
    public static $rowtypetopagemapping = 'null';
    public static $subtitle = 'allergen';
    public static $showsubtitle = NULL;
    public static $dbfield = 'allergen';
    public static $defaultvalue = NULL;
    public static $nullallowed = NULL;
    public static $dbtrigger = NULL;
    public static $index = NULL;
    public static $placeholder = NULL;
    public static $required = NULL;
    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n13050n';
    public static $parentElementid = 'n13048n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    public static $childrenElementIds = [
];
/*GENERATEDBYBUILDER]*/

//    public static function getJs(){
//        $result = parent::getJs();
//        $result['Actinum.Application.CustomJs.n13050n'] =
//<<<'JS'
//Actinum.Application.CustomElements.n13050n = new Class({ Extends: Actinum.Application.Elements.Forminputdropdown,
//});
//JS;
//        return $result;
//    }
//
//
//
//    public static function getCss(){
//        $result = parent::getCss();
//        $result['Actinum.Application.CustomCss.n13050n'] =
//<<<'CSS'
//CSS;
//        return $result;
//    }


        


}

}
?>