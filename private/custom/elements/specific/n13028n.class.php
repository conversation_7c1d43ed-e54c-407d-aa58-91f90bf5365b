<?php
namespace nl\actinum\custom\elements\specific {



/*[GENERATEDBYBUILDER_CLASSNAME*/
class n13028n extends \nl\actinum\custom\elements\generic\Blockcontainer
/*GENERATEDBYBUILDER_CLASSNAME]*/
    //implements \nl\actinum\framework\application\interfaces\IHasCustomClientSideCode
                                                                                                                            {

/*[GENERATEDBYBUILDER*/

    public static $additionalcssclasses = NULL;
    public static $style = '._this_{width:25%;}';
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n13028n';
    public static $parentElementid = 'n13007n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    public static $childrenElementIds = [
];
/*GENERATEDBYBUILDER]*/

//    public static function getJs(){
//        $result = parent::getJs();
//        $result['Actinum.Application.CustomJs.n13028n'] =
//<<<'JS'
//Actinum.Application.CustomElements.n13028n = new Class({ Extends: Actinum.Application.Elements.Blockcontainer,
//});
//JS;
//        return $result;
//    }
//
//
//
//    public static function getCss(){
//        $result = parent::getCss();
//        $result['Actinum.Application.CustomCss.n13028n'] =
//<<<'CSS'
//CSS;
//        return $result;
//    }


        


}

}
?>