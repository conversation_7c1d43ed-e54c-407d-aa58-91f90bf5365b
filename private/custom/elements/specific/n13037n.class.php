<?php
namespace nl\actinum\custom\elements\specific {



/*[GENERATEDBYBUILDER_CLASSNAME*/
class n13037n extends \nl\actinum\custom\elements\generic\Forminputsearch
/*GENERATEDBYBUILDER_CLASSNAME]*/
    //implements \nl\actinum\framework\application\interfaces\IHasCustomClientSideCode
                                                                                                                            {

/*[GENERATEDBYBUILDER*/

    public static $tablename = 'article';
    public static $selectdql = 'article.id';
    public static $aliaspostfix = NULL;
    public static $detailpage = NULL;
    public static $rowtypetopagemapping = 'null';
    public static $allownondatabaseentries = NULL;
    public static $findingridpopupblock = NULL;
    public static $subtitle = NULL;
    public static $showsubtitle = NULL;
    public static $dbfield = 'article';
    public static $defaultvalue = NULL;
    public static $nullallowed = NULL;
    public static $dbtrigger = NULL;
    public static $index = NULL;
    public static $placeholder = NULL;
    public static $required = NULL;
    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n13037n';
    public static $parentElementid = 'n13035n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    public static $childrenElementIds = [
];
/*GENERATEDBYBUILDER]*/

//    public static function getJs(){
//        $result = parent::getJs();
//        $result['Actinum.Application.CustomJs.n13037n'] =
//<<<'JS'
//Actinum.Application.CustomElements.n13037n = new Class({ Extends: Actinum.Application.Elements.Forminputsearch,
//});
//JS;
//        return $result;
//    }
//
//
//
//    public static function getCss(){
//        $result = parent::getCss();
//        $result['Actinum.Application.CustomCss.n13037n'] =
//<<<'CSS'
//CSS;
//        return $result;
//    }


        


}

}
?>