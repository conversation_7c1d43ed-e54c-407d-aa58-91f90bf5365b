<?php
namespace nl\actinum\custom\elements\specific {



/*[GENERATEDBYBUILDER_CLASSNAME*/
class n13005n extends \nl\actinum\custom\elements\generic\State
/*GENERATEDBYBUILDER_CLASSNAME]*/
    //implements \nl\actinum\framework\application\interfaces\IHasCustomClientSideCode
                                                                                                                            {

/*[GENERATEDBYBUILDER*/

    public static $add = 'n13006n';
    public static $edit = 'n13006n';
    public static $view = 'n13006n';
    public static $initialstate = true;
    public static $x = 100;
    public static $y = 100;
    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n13005n';
    public static $parentElementid = 'n13004n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    public static $childrenElementIds = [
];
/*GENERATEDBYBUILDER]*/

//    public static function getJs(){
//        $result = parent::getJs();
//        $result['Actinum.Application.CustomJs.n13005n'] =
//<<<'JS'
//Actinum.Application.CustomElements.n13005n = new Class({ Extends: Actinum.Application.Elements.State,
//});
//JS;
//        return $result;
//    }
//
//
//
//    public static function getCss(){
//        $result = parent::getCss();
//        $result['Actinum.Application.CustomCss.n13005n'] =
//<<<'CSS'
//CSS;
//        return $result;
//    }


        


}

}
?>