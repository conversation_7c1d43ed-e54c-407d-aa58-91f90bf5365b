<?php
namespace nl\actinum\custom\elements\specific {



/*[GENERATEDBYBUILDER_CLASSNAME*/
class n13008n extends \nl\actinum\custom\elements\generic\Blockcontainer
/*GENERATEDBYBUILDER_CLASSNAME]*/
    //implements \nl\actinum\framework\application\interfaces\IHasCustomClientSideCode
                                                                                                                            {

/*[GENERATEDBYBUILDER*/

    public static $additionalcssclasses = NULL;
    public static $style = '._this_{width:75%; padding-right:30px;}';
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n13008n';
    public static $parentElementid = 'n13007n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    public static $childrenElementIds = [
  0 => 'n13009n',
  1 => 'n13044n',
];
/*GENERATEDBYBUILDER]*/

//    public static function getJs(){
//        $result = parent::getJs();
//        $result['Actinum.Application.CustomJs.n13008n'] =
//<<<'JS'
//Actinum.Application.CustomElements.n13008n = new Class({ Extends: Actinum.Application.Elements.Blockcontainer,
//});
//JS;
//        return $result;
//    }
//
//
//
//    public static function getCss(){
//        $result = parent::getCss();
//        $result['Actinum.Application.CustomCss.n13008n'] =
//<<<'CSS'
//CSS;
//        return $result;
//    }


        


}

}
?>