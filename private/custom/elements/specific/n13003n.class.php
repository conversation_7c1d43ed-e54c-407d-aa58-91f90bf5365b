<?php
namespace nl\actinum\custom\elements\specific {



/*[GENERATEDBYBUILDER_CLASSNAME*/
class n13003n extends \nl\actinum\custom\elements\generic\Tablepage
/*GENERATEDBYBUILDER_CLASSNAME]*/
    //implements \nl\actinum\framework\application\interfaces\IHasCustomClientSideCode
                                                                                                                            {

/*[GENERATEDBYBUILDER*/

    public static $title = 'articleallergen';
    public static $tablename = 'articleallergen';
    public static $Tablepagetype = NULL;
    public static $extends = NULL;
    public static $elementid = 'n13003n';
    public static $parentElementid = 'n11032n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    public static $childrenElementIds = [
  0 => 'n13004n',
  1 => 'n13006n',
];
/*GENERATEDBYBUILDER]*/

//    public static function getJs(){
//        $result = parent::getJs();
//        $result['Actinum.Application.CustomJs.n13003n'] =
//<<<'JS'
//Actinum.Application.CustomElements.n13003n = new Class({ Extends: Actinum.Application.Elements.Tablepage,
//});
//JS;
//        return $result;
//    }
//
//
//
//    public static function getCss(){
//        $result = parent::getCss();
//        $result['Actinum.Application.CustomCss.n13003n'] =
//<<<'CSS'
//CSS;
//        return $result;
//    }


        


}

}
?>