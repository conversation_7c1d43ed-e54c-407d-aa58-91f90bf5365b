<?php
namespace nl\actinum\custom\elements\specific {



/*[GENERATEDBYBUILDER_CLASSNAME*/
class n13053n extends \nl\actinum\custom\elements\generic\Forminputdropdown
/*GENERATEDBYBUILDER_CLASSNAME]*/
    //implements \nl\actinum\framework\application\interfaces\IHasCustomClientSideCode
                                                                                                                            {

/*[GENERATEDBYBUILDER*/

    public static $tablename = 'allergennotification';
    public static $selectdql = 'allergennotification.name';
    public static $aliaspostfix = NULL;
    public static $detailpage = NULL;
    public static $rowtypetopagemapping = 'null';
    public static $subtitle = 'allergennotification';
    public static $showsubtitle = NULL;
    public static $dbfield = 'allergennotification';
    public static $defaultvalue = NULL;
    public static $nullallowed = NULL;
    public static $dbtrigger = NULL;
    public static $index = NULL;
    public static $placeholder = NULL;
    public static $required = NULL;
    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n13053n';
    public static $parentElementid = 'n13051n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    public static $childrenElementIds = [
];
/*GENERATEDBYBUILDER]*/

//    public static function getJs(){
//        $result = parent::getJs();
//        $result['Actinum.Application.CustomJs.n13053n'] =
//<<<'JS'
//Actinum.Application.CustomElements.n13053n = new Class({ Extends: Actinum.Application.Elements.Forminputdropdown,
//});
//JS;
//        return $result;
//    }
//
//
//
//    public static function getCss(){
//        $result = parent::getCss();
//        $result['Actinum.Application.CustomCss.n13053n'] =
//<<<'CSS'
//CSS;
//        return $result;
//    }


        


}

}
?>