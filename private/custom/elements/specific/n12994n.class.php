<?php
namespace nl\actinum\custom\elements\specific {



/*[GENERATEDBYBUILDER_CLASSNAME*/
class n12994n extends \nl\actinum\custom\elements\generic\Header3
/*GENERATEDBYBUILDER_CLASSNAME]*/
    //implements \nl\actinum\framework\application\interfaces\IHasCustomClientSideCode
                                                                                                                            {

/*[GENERATEDBYBUILDER*/

    public static $title = 'Dynamische kopjes';
    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n12994n';
    public static $parentElementid = 'n10453n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    public static $childrenElementIds =  [
];
/*GENERATEDBYBUILDER]*/

//    public static function getJs(){
//        $result = parent::getJs();
//        $result['Actinum.Application.CustomJs.n12994n'] =
//<<<'JS'
//Actinum.Application.CustomElements.n12994n = new Class({ Extends: Actinum.Application.Elements.Header3,
//});
//JS;
//        return $result;
//    }



    public static function getCss(){
        $result = parent::getCss();
        $result['Actinum.Application.CustomCss.n12994n'] =
<<<'CSS'

    .n12994n {
        display: none;
    }

CSS;
        return $result;
    }


        


}

}
?>