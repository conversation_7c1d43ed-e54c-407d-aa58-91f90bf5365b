<?php
namespace nl\actinum\custom\elements\specific {



/*[GENERATEDBYBUILDER_CLASSNAME*/
class n13054n extends \nl\actinum\custom\elements\generic\Forminputcontainer
/*GENERATEDBYBUILDER_CLASSNAME]*/
    //implements \nl\actinum\framework\application\interfaces\IHasCustomClientSideCode
                                                                                                                            {

/*[GENERATEDBYBUILDER*/

    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n13054n';
    public static $parentElementid = 'n13044n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    public static $childrenElementIds = [
  0 => 'n13055n',
  1 => 'n13056n',
];
/*GENERATEDBYBUILDER]*/

//    public static function getJs(){
//        $result = parent::getJs();
//        $result['Actinum.Application.CustomJs.n13054n'] =
//<<<'JS'
//Actinum.Application.CustomElements.n13054n = new Class({ Extends: Actinum.Application.Elements.Forminputcontainer,
//});
//JS;
//        return $result;
//    }
//
//
//
//    public static function getCss(){
//        $result = parent::getCss();
//        $result['Actinum.Application.CustomCss.n13054n'] =
//<<<'CSS'
//CSS;
//        return $result;
//    }


        


}

}
?>