<?php
namespace nl\actinum\custom\elements\specific {



/*[GENERATEDBYBUILDER_CLASSNAME*/
class n13006n extends \nl\actinum\custom\elements\generic\Subpagegroup
/*GENERATEDBYBUILDER_CLASSNAME]*/
    //implements \nl\actinum\framework\application\interfaces\IHasCustomClientSideCode
                                                                                                                            {

/*[GENERATEDBYBUILDER*/

    public static $title = 'Default';
    public static $elementid = 'n13006n';
    public static $parentElementid = 'n13003n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    public static $childrenElementIds = [
  0 => 'n13007n',
];
/*GENERATEDBYBUILDER]*/

//    public static function getJs(){
//        $result = parent::getJs();
//        $result['Actinum.Application.CustomJs.n13006n'] =
//<<<'JS'
//Actinum.Application.CustomElements.n13006n = new Class({ Extends: Actinum.Application.Elements.Subpagegroup,
//});
//JS;
//        return $result;
//    }
//
//
//
//    public static function getCss(){
//        $result = parent::getCss();
//        $result['Actinum.Application.CustomCss.n13006n'] =
//<<<'CSS'
//CSS;
//        return $result;
//    }


        


}

}
?>