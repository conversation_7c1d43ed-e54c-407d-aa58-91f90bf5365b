<?php
namespace nl\actinum\custom\elements\specific {



/*[GENERATEDBYBUILDER_CLASSNAME*/
class n13044n extends \nl\actinum\custom\elements\generic\Form
/*GENERATEDBYBUILDER_CLASSNAME]*/
    //implements \nl\actinum\framework\application\interfaces\IHasCustomClientSideCode
                                                                                                                            {

/*[GENERATEDBYBUILDER*/

    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n13044n';
    public static $parentElementid = 'n13008n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    public static $childrenElementIds = [
  0 => 'n13035n',
  1 => 'n13048n',
  2 => 'n13051n',
  3 => 'n13054n',
];
/*GENERATEDBYBUILDER]*/

//    public static function getJs(){
//        $result = parent::getJs();
//        $result['Actinum.Application.CustomJs.n13044n'] =
//<<<'JS'
//Actinum.Application.CustomElements.n13044n = new Class({ Extends: Actinum.Application.Elements.Form,
//});
//JS;
//        return $result;
//    }
//
//
//
//    public static function getCss(){
//        $result = parent::getCss();
//        $result['Actinum.Application.CustomCss.n13044n'] =
//<<<'CSS'
//CSS;
//        return $result;
//    }


        


}

}
?>